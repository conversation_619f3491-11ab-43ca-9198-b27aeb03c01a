#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
Copyright (c) 2019 Tianxiadatong, Co., Ltd. All Rights Reserved.

This module defines database models.

Author: UwU (<EMAIL>)
Date: 2025-07-03 17:23:06
"""

import datetime
import enum
from typing import Literal

from sqlalchemy import Column, Integer, String, Text, Boolean, Date, BigInteger, DateTime, func, Enum, SmallInteger, \
    JSON, Numeric, CHAR
from app.core.database import Base


class BbTFundInfo(Base):
    __tablename__ = 'bb_tfundinfo'

    company_id = Column(Integer, default=0)
    fund_id = Column(Integer, primary_key=True)
    fund_code = Column(String(32), default='')
    fund_name = Column(String(128), default='')
    fund_caption = Column(String(128), default='')
    fund_type = Column(Integer, default=0)
    fund_status = Column(CHAR(1), default='')
    fund_trustee_id = Column(Integer, default=0)
    ta_type = Column(String(3), default='')
    management_fee_ratio = Column(Numeric(15, 8), default=0)
    custodian_fee_ratio = Column(Numeric(15, 8), default=0)
    fund_share = Column(Numeric(20, 4), default=0)
    net_price_digits = Column(Integer, default=0)
    invest_direction = Column(CHAR(1), default='')
    target_etf_inter_code = Column(Integer, default=0)
    end_date = Column(Integer, default=0)
    establish_date = Column(Integer, default=0)
    interbank_id = Column(Integer, default=0)
    remark = Column(String(1024), default='')
    input_date = Column(Integer, default=0)
    modify_date = Column(Integer, default=0)
    trust_account_id = Column(Integer, default=0)
    seat_group_id = Column(Integer, default=0)
    seat_group_id_list = Column(String(512), default='')
    object_code = Column(String(128), default='')
    fa_value_model_id = Column(Integer, default=0)
    tax_type = Column(CHAR(1), default='')
    fund_cust_id = Column(Integer, default=0)
    net_price_precision_dealmode = Column(CHAR(1), default='1')
    fund_invest_property = Column(String(3), default='')

    __table_args__ = (
        # Unique constraint
        {'sqlite_autoincrement': True},
    )


class BbTTraderivalLinkman(Base):
    __tablename__ = 'bb_ttraderivallinkman'

    org_id = Column(Integer, default=0)
    contact_id = Column(Integer, primary_key=True)
    rival_trader_type = Column(CHAR(1), default='')
    linkman = Column(String(128), default='')
    address = Column(String(256), default='')
    phone_no = Column(String(32), default='')
    fax_no = Column(String(128), default='')
    email = Column(String(128), default='')
    company_id = Column(Integer, default=0)
    qq = Column(String(32), default='')
    msn_id = Column(String(64), default='')
    rival_tradercode = Column(String(32), default='')
    zip_code = Column(String(16), default='')
    linkman_no = Column(String(128), default='')
    rival_id = Column(Integer, default=0)


class BbTHgRegister(Base):
    __tablename__ = 'bb_thgregister'

    business_date = Column(Integer, default=0)
    serial_no = Column(Integer, primary_key=True, default=0)
    company_id = Column(Integer, default=0)
    fund_id = Column(Integer, default=0)
    asset_id = Column(Integer, default=0)
    combi_id = Column(Integer, default=0)
    market_no = Column(Integer, primary_key=True, default=0)
    report_code = Column(String(32), default='')
    stock_name = Column(String(128), default='')
    deal_amount = Column(Numeric(20, 4), default=0)
    hg_rate = Column(Numeric(15, 8), default=0)
    deal_balance = Column(Numeric(18, 2), default=0)
    entrust_direction = Column(Integer, default=0)
    hg_fee = Column(Numeric(18, 2), default=0)
    hg_interest = Column(Numeric(18, 2), default=0)
    hg_days = Column(Numeric(16, 2), default=0)
    clear_speed = Column(Integer, default=0)
    gh_process_flag = Column(CHAR(1), default='')
    gh_settle_flag = Column(CHAR(1), default='')
    deal_no = Column(String(64), default='')
    deal_confirm_no = Column(Integer, default=0)
    hg_deal_date = Column(Integer, primary_key=True, default=0)
    pendsettle_serial_no = Column(Integer, default=0)
    actual_gh_date = Column(Integer, default=0)
    bind_seat = Column(String(20), default='')
    busin_flag = Column(Integer, default=0)
    capital_account_id = Column(Integer, default=0)
    clear_type = Column(CHAR(1), default='')
    deal_price = Column(Numeric(20, 12), default=0)
    expire_interest = Column(Numeric(22, 12), default=0)
    expire_net_price = Column(Numeric(20, 12), default=0)
    expire_settle_date = Column(Integer, default=0)
    first_interest = Column(Numeric(18, 2), default=0)
    first_net_price = Column(Numeric(20, 12), default=0)
    ins_id = Column(Integer, default=0)
    ins_modify_index = Column(Integer, default=0)
    invest_type = Column(CHAR(1), default='')
    legal_gh_date = Column(Integer, default=0)
    realdeal_serial_no = Column(Integer, default=0)
    second_settle_point = Column(Integer, default=0)
    settle_type = Column(CHAR(1), default='')
    trade_rival_no = Column(Integer, default=0)
    underlying_deal_amount = Column(Numeric(20, 4), default=0)
    settle_date = Column(Integer, default=0)
    settle_point = Column(Integer, default=0)
    source_flag = Column(CHAR(1), default='')
    stockholder_id = Column(String(20), default='')
    trade_seat = Column(String(20), default='')
    now_interest = Column(Numeric(18, 2), default=0)
    contract_no = Column(String(64), default='')
    rival_id = Column(Integer, default=0)
    rival_seat = Column(String(20), default='')
    ghins_process_flag = Column(CHAR(1), default='')
    rival_tradercode = Column(String(32), default='')
    contact_id = Column(Integer, default=0)
    expire_deal_no = Column(String(64), default='')
    expire_settle_balance = Column(Numeric(18, 2), default=0)
    udly_stock_ati_bal = Column(Numeric(18, 2), default=0)
    breach_limit_term = Column(Integer, nullable=True, default=0)
    sz_dispose_flag = Column(CHAR(1), nullable=True, default='')
    first_settle_date = Column(Integer, default=0)


class BbTTraderival(Base):
    __tablename__ = 'bb_ttraderival'

    rival_id = Column(Integer, primary_key=True, default=0)
    rival_code = Column(String(128), nullable=True, default='')
    org_id = Column(Integer, nullable=True, default=0)
    traderival_name = Column(String(256), nullable=True, default='')
    rival_fullname = Column(String(512), nullable=True, default='')
    rival_type = Column(CHAR(1), default='')
    interbank_id = Column(Integer, nullable=True, default=0)
    bank_trusteeship_account = Column(String(32), nullable=True, default='')
    shclearing_account = Column(String(128), nullable=True, default='')
    interbank_organ_code = Column(String(32), nullable=True, default='')
    traderival_status = Column(CHAR(1), nullable=True, default='')
    capital = Column(Numeric(18, 2), nullable=True, default=0)
    total_value = Column(Numeric(18, 2), nullable=True, default=0)
    net_value = Column(Numeric(18, 2), nullable=True, default=0)
    remark = Column(String(1024), nullable=True, default='')
    spell = Column(String(1024), nullable=True, default='')
    rival_companycode = Column(String(32), nullable=True, default='')
    company_id = Column(Integer, nullable=True, default=0)
    bank_id = Column(String(32), nullable=True, default='')
    issuer_id = Column(Integer, nullable=True, default=None)
    asset_size = Column(Numeric(16, 2), nullable=True, default=0)
    parent_org_interbank_id = Column(Integer, nullable=True, default=0)
    org_main_flag = Column(CHAR(1), nullable=True, default='')
    shclearing_account_no = Column(String(32), nullable=True, default='')
    chinabond_account_no = Column(String(32), nullable=True, default='')
    bank_account_id_list = Column(String(1024), nullable=True, default='')
    marketmaker_type = Column(CHAR(1), nullable=True, default='')
    private_product_flag = Column(CHAR(1), nullable=True, default='')


class VarietyEnum(str, enum.Enum):
    现券 = '现券'
    回购 = '回购'


class MarketEnum(str, enum.Enum):
    银行间 = '银行间'
    深圳 = '深圳'
    上海 = '上海'


class Audit(Base):
    __tablename__ = "audit"

    id = Column(Integer, primary_key=True, comment="人工审核表")
    variety = Column(Enum(VarietyEnum), nullable=True, comment="品种")
    market = Column(Enum(MarketEnum), nullable=True, comment="市场")
    classification = Column(String(50), nullable=True, comment="")
    execution = Column(DateTime, nullable=True, comment="执行日")
    production = Column(String(255), nullable=True, comment="产品名称")
    # 新增标准产品名称
    production_fund_caption = Column(String(255), nullable=True, comment="标准产品名称")
    production_id = Column(String(255), nullable=True, comment="产品ID")
    direction = Column(SmallInteger, nullable=True, comment="方向（买入买出）")
    three_party_sequel = Column(String(255), comment="三方续作")
    security_code = Column(String(100), comment="证券代码")
    security_name = Column(String(255), comment="证券名")
    yield_rate = Column(String(200), comment="收益率")
    net_price = Column(String(100), comment="净价/委托价格")
    full_price = Column(String(100), comment="全价")
    yield_to_maturity = Column(String(100), comment="到期收益率")
    option_yield = Column(String(100), comment="行权收益率")
    quantity = Column(String(50), comment="数量")
    denomination = Column(String(150), comment="金额/面额")
    liquidation_speed = Column(String(200), default='T+0', comment="清算速度")
    rival = Column(String(255), comment="对手方")
    rival_interbank_organ_code = Column(String(255), comment="银行间对手方机构编号")
    rival_trader = Column(String(200), comment="对手方交易员")
    rival_seat = Column(String(200), comment="对手席位")
    rival_trader_id = Column(String(200), comment="对手交易员号")
    agreement_number = Column(String(200), comment="约定号")
    payment_method = Column(String(200), comment="结算方式")
    declaration_type = Column(String(200), comment="申报类型")
    rival_dealer_code = Column(String(200), comment="对手交易商代码")
    trader_entity_code = Column(String(150), comment="交易商主体代码")
    rival_trader_code = Column(String(150), comment="对手交易员代码")
    deadline = Column(String(100), comment="期限")
    violation_grace_period = Column(String(150), comment="违约宽限期")
    supplementary_term = Column(Text, comment="补充条款")
    share_bonus_method = Column(String(200), comment="分红方式")
    distribution_channel = Column(String(200), comment="销售渠道")
    pledge_coupon_code = Column(String(150), comment="质押券代码")
    pledge_coupon_name = Column(String(200), comment="质押券名称")
    pledge_quantity = Column(String(150), comment="质押数量")
    discount_rate = Column(String(100), comment="折扣率")
    message = Column(Integer, nullable=False, comment="原始消息")
    instruction_id = Column(String(64), nullable=False, comment="指令ID，懂的都懂")
    guid = Column(String(64), nullable=True, comment="o45请求唯一标识")
    status = Column(SmallInteger, nullable=False,
                    comment="未处理0/已撤回(未处理)1/已变更(未处理)2/已处理(失效)3/已处理(确认)4/已处理撤回(禁止还原)5/无权限（未处理)6/无权限已处理7")
    auditor_id = Column(Integer, nullable=True, comment="审核人")
    Remark = Column(Text, nullable=True, comment="<UNK>")
    # O45 返回值
    o45_return_json = Column(Text, nullable=True, comment="O45返回值")
    created_at = Column(DateTime, nullable=False, default=datetime.datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow,
                        comment="更新时间")
    rival_code = Column(String(255), comment="o45对手交易编号")
    interbank_ins_quote_type = Column(String(64), default='3', comment="银行间指令报价方式")
    entrust_price_type = Column(String(64), default='f', comment="现卷上海指定对手方报价申报")


class Consigner(Base):
    __tablename__ = "consigner"

    id = Column(Integer, primary_key=True, autoincrement=True)
    consigner = Column(String(200), nullable=False)
    is_deleted = Column(Boolean, nullable=False, default=False)


class Message(Base):
    __tablename__ = 'message'
    id = Column(Integer, primary_key=True, index=True)
    sender = Column(String(200), nullable=False, comment="发送者")
    receiver = Column(String(200), nullable=False, comment="接收者")
    content = Column(Text, nullable=False, comment="内容")
    position = Column(String(255), nullable=False, comment="哪个群发的/还是私聊发的")
    type = Column(SmallInteger, nullable=False, comment="什么类型的消息?Text/image?是不是在回复别人？")  # 00 ~ 11
    reply = Column(Integer, nullable=True, comment="回复哪条消息")
    sequence = Column(BigInteger, nullable=True, comment="唯一信息编号（机器人端用）")
    date = Column(DateTime, nullable=False, comment="<UNK>", default=datetime.datetime.now)
    channel = Column(String(200), nullable=True, comment="marker for recognize websocket client")
    role = Column(Enum('user', 'robot', name='role_type'), nullable=False, comment="<UNK>", default="user")
    result = Column(Text, nullable=True, comment="意图识别响应字段")


# 日志表
class Log(Base):
    __tablename__ = "log"
    id = Column(Integer, primary_key=True, autoincrement=True)  # auto ID assignment
    operator = Column(String(255), nullable=False)  # 谁干的
    target = Column(String(255), nullable=False)  # 干了谁
    action = Column(String(255), nullable=False)  # 干什么
    result = Column(Boolean, nullable=False)  # 结果
    clause = Column(Text, nullable=True)  # 附加信息
    object = Column(String(50), nullable=False, default="业务审核")


class Group(Base):
    __tablename__ = "group"  # 这里是表名，注意是SQL关键字，使用时需要加反引号

    id = Column(Integer, primary_key=True, autoincrement=True)
    type = Column(Enum("QQ", "微信", name="group_type"), nullable=False)
    name = Column(String(200), nullable=False)
    number = Column(String(200), nullable=True, comment="群号")
    listening = Column(Boolean, nullable=False, comment="是否启用（即监听）")
    robot = Column(JSON, nullable=False)


class GroupConsigner(Base):
    __tablename__ = "group_consigner"

    id = Column(Integer, primary_key=True, autoincrement=True)
    group_id = Column(Integer, nullable=False)
    consigner_id = Column(Integer, nullable=False)
    is_deleted = Column(Boolean, default=False)


class GroupProduction(Base):
    __tablename__ = 'group_production'

    id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    group_id = Column(Integer, nullable=False)
    production_id = Column(Integer, nullable=False)
    is_deleted = Column(Boolean, default=False)


class GroupTrader(Base):
    __tablename__ = 'group_trader'

    id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    group_id = Column(Integer, nullable=False)
    trader_id = Column(Integer, nullable=False)
    is_deleted = Column(Boolean, default=False)


class GroupMember(Base):
    __tablename__ = "group_member"

    id = Column(Integer, primary_key=True, autoincrement=True)
    number = Column(String(200), nullable=False)  # 群员的账号
    name = Column(String(200), nullable=False)
    group_id = Column(Integer, nullable=False)
    fishing_order_permission = Column(Boolean, nullable=False, default=False, comment="是否有捞单权限")
    consultation_permission = Column(Boolean, nullable=False, default=False, comment="是否有咨询权限")
    inquiry_permission = Column(Boolean, nullable=False, default=False, comment="是否有询价权限")
    role = Column(Enum('regular', 'trader', 'robot', name="group_member_role"), nullable=False)


class GroupRobot(Base):
    __tablename__ = 'group_robot'

    id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    robot_id = Column(Integer, nullable=False)
    group_id = Column(Integer, nullable=False)


class Script(Base):
    __tablename__ = "script"

    id = Column(Integer, primary_key=True, index=True, comment="话术表", autoincrement=True)
    content = Column(Text(), nullable=False)
    environment = Column(Text(), nullable=False)  # 场景（写死）
    enable = Column(Boolean, nullable=False)


# 机器人
class Robot(Base):
    __tablename__ = "robot"

    id = Column(Integer, primary_key=True, autoincrement=True)
    type = Column(Enum("微信", "QQ", name="robot_type"), nullable=False)
    uni = Column(String(100), nullable=False)  # 独一无二的编号（需求要的）
    account = Column(String(255), nullable=False)
    gender = Column(Boolean, nullable=False)  # 1/0 -> 男/女
    tel = Column(String(100), nullable=False)
    email = Column(String(255), nullable=False)
    status = Column(Boolean, nullable=False, default=False)  # 有没有被下线了
    enable = Column(Boolean, nullable=False, default=True)

    class Config:
        from_attribute = True


class UserRole(str, enum.Enum):
    """用户角色枚举类"""
    salesman = 'salesman'
    admin = 'admin'
    root = 'root'


# 用户
class User(Base):
    __tablename__ = "user"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    username = Column(String(100), nullable=False)  # 账号
    nickname = Column(String(100), nullable=False)  # 昵称
    password = Column(String(100), nullable=False)
    role = Column(Enum(UserRole), nullable=False, default=UserRole.salesman)  # 角色
    tel = Column(String(60), nullable=False)  # 电话号码
    phone = Column(String(60), nullable=False)  # 手机号码
    email = Column(String(100), nullable=False)
    active = Column(Boolean, nullable=False, default=True)  # 是否启用
    disable = Column(Boolean, nullable=False, default=False)  # 是否封禁
    qq = Column(String(255), nullable=True)
    wechat = Column(String(255), nullable=True)

    class Config:
        from_attribute = True
