#!/usr/bin/env python
# -*- coding:utf-8 -*-

"""
Copyright (c) 2019 Tianxiadatong, Co., Ltd. All Rights Reserved.

This module defines pydantic models.

Author: UwU (<EMAIL>)
Date: 2025-07-03 17:23:06
"""
import re
from datetime import datetime
from typing import Optional, Literal, List
from pydantic import BaseModel, EmailStr, constr, Field, validator


# 每个属性的意义，详见 models.py

class BbTFundInfoSchema(BaseModel):
    company_id: int = 0
    fund_id: int
    fund_code: str
    fund_name: Optional[str] = ''
    fund_caption: Optional[str] = ''
    fund_type: int = 0
    fund_status: str
    fund_trustee_id: Optional[int] = 0
    ta_type: Optional[str] = ''
    management_fee_ratio: Optional[float] = 0
    custodian_fee_ratio: Optional[float] = 0
    fund_share: Optional[float] = 0
    net_price_digits: Optional[int] = 0
    invest_direction: Optional[str] = ''
    target_etf_inter_code: Optional[int] = 0
    end_date: Optional[int] = 0
    establish_date: Optional[int] = 0
    interbank_id: Optional[int] = 0
    remark: Optional[str] = ''
    input_date: Optional[int] = 0
    modify_date: Optional[int] = 0
    trust_account_id: Optional[int] = 0
    seat_group_id: Optional[int] = 0
    seat_group_id_list: Optional[str] = ''
    object_code: Optional[str] = ''
    fa_value_model_id: Optional[int] = 0
    tax_type: str
    fund_cust_id: int
    net_price_precision_dealmode: str
    fund_invest_property: Optional[str] = ''

    class Config:
        from_attribute = True

class BbTTraderivalLinkmanSchema(BaseModel):
    org_id: int = 0
    contact_id: int
    rival_trader_type: str
    linkman: str
    address: Optional[str] = ''
    phone_no: Optional[str] = ''
    fax_no: Optional[str] = ''
    email: Optional[str] = ''
    company_id: Optional[int] = 0
    qq: Optional[str] = ''
    msn_id: Optional[str] = ''
    rival_tradercode: str
    zip_code: Optional[str] = ''
    linkman_no: Optional[str] = ''
    rival_id: int = 0

    class Config:
        from_attribute = True

class BbTHgRegisterSchema(BaseModel):
    business_date: int = 0
    serial_no: int
    company_id: int = 0
    fund_id: int = 0
    asset_id: int = 0
    combi_id: int = 0
    market_no: int
    report_code: str
    stock_name: str
    deal_amount: float
    hg_rate: float
    deal_balance: float
    entrust_direction: int
    hg_fee: float
    hg_interest: float
    hg_days: float
    clear_speed: int
    gh_process_flag: str
    gh_settle_flag: str
    deal_no: str
    deal_confirm_no: int
    hg_deal_date: int
    pendsettle_serial_no: int
    actual_gh_date: int
    bind_seat: str
    busin_flag: int
    capital_account_id: int
    clear_type: str
    deal_price: float
    expire_interest: float
    expire_net_price: float
    expire_settle_date: int
    first_interest: float
    first_net_price: float
    ins_id: int
    ins_modify_index: int
    invest_type: str
    legal_gh_date: int
    realdeal_serial_no: int
    second_settle_point: int
    settle_type: str
    trade_rival_no: int
    underlying_deal_amount: float
    settle_date: int
    settle_point: int
    source_flag: str
    stockholder_id: str
    trade_seat: str
    now_interest: float
    contract_no: str
    rival_id: int
    rival_seat: str
    ghins_process_flag: str
    rival_tradercode: str
    contact_id: int
    expire_deal_no: str
    expire_settle_balance: float
    udly_stock_ati_bal: float
    breach_limit_term: Optional[int] = 0
    sz_dispose_flag: Optional[str] = ''
    first_settle_date: int

    class Config:
        from_attribute = True

class BbTTraderivalSchema(BaseModel):
    rival_id: int
    rival_code: Optional[str] = ''
    org_id: Optional[int] = 0
    traderival_name: Optional[str] = ''
    rival_fullname: Optional[str] = ''
    rival_type: str
    interbank_id: Optional[int] = 0
    bank_trusteeship_account: Optional[str] = ''
    shclearing_account: Optional[str] = ''
    interbank_organ_code: Optional[str] = ''
    traderival_status: Optional[str] = ''
    capital: Optional[float] = 0.0
    total_value: Optional[float] = 0.0
    net_value: Optional[float] = 0.0
    remark: Optional[str] = ''
    spell: Optional[str] = ''
    rival_companycode: Optional[str] = ''
    company_id: Optional[int] = 0
    bank_id: Optional[str] = ''
    issuer_id: Optional[int] = None
    asset_size: Optional[float] = 0.0
    parent_org_interbank_id: Optional[int] = 0
    org_main_flag: Optional[str] = ''
    shclearing_account_no: Optional[str] = ''
    chinabond_account_no: Optional[str] = ''
    bank_account_id_list: Optional[str] = ''
    marketmaker_type: Optional[str] = ''
    private_product_flag: Optional[str] = ''

    class Config:
        from_attribute = True

class InstructionBase(BaseModel):
    task_id: str

class InstructionCreate(InstructionBase):
    pass

class InstructionRead(InstructionBase):
    id: str

    class Config:
        from_attribute = True

class TaskBase(BaseModel):
    message_id: int

class TaskCreate(TaskBase):
    pass

class TaskRead(TaskBase):
    id: int

    class Config:
        from_attribute = True

class GroupConsignerBase(BaseModel):
    group_id: int
    consigner_id: int
    is_deleted: bool

class GroupConsignerCreate(GroupConsignerBase):
    pass

class GroupConsignerRead(GroupConsignerBase):
    id: int

    class Config:
        from_attribute = True


class ConsignerBase(BaseModel):
    consigner: str

class ConsignerCreate(ConsignerBase):
    pass

class ConsignerRead(ConsignerBase):
    id: int

    class Config:
        from_attribute = True


def clean_value(v):
    if isinstance(v, str):
        cleaned = re.sub(r"[\\\[\]']", '', v)
        cleaned = cleaned.replace(',', '、').strip()
        return cleaned
    return v

class AuditBase(BaseModel):
    variety: Literal['现券', '回购']
    classification:str
    market: Literal['银行间', '上海', '深圳']
    execution: datetime
    production: str
    production_fund_caption: Optional[str]
    production_id: str
    Remark : Optional[str]
    direction: int
    three_party_sequel: Optional[str]
    security_code: Optional[str]
    security_name: Optional[str]
    yield_rate: Optional[str]
    net_price: Optional[str]
    full_price: Optional[str]
    yield_to_maturity: Optional[str]
    option_yield: Optional[str]
    quantity: Optional[str]
    denomination: Optional[str]
    liquidation_speed: Optional[str]
    rival: Optional[str]
    rival_trader: Optional[str]
    rival_seat: Optional[str]
    rival_trader_id: Optional[str]
    agreement_number: Optional[str]
    payment_method: Optional[str]
    declaration_type: Optional[str]
    rival_dealer_code: Optional[str]
    trader_entity_code: Optional[str]
    rival_trader_code: Optional[str]
    deadline: Optional[str]
    violation_grace_period: Optional[str]
    supplementary_term: Optional[str]
    share_bonus_method: Optional[str]
    distribution_channel: Optional[str]
    pledge_coupon_code: Optional[str]
    pledge_coupon_name: Optional[str]
    pledge_quantity: Optional[str]
    discount_rate: Optional[str]
    message: int
    instruction_id: str
    status: int
    auditor_id: Optional[int] = None
    pageTap:str

    class Config:
        from_attribute = True

    @validator("auditor_id", pre=True)
    def empty_str_to_none(cls, v):
        return None if v == "" else v



    # @validator('pledge_coupon_code', pre=True)
    # def convert_pledge_coupon_code(cls, v):
    #     if isinstance(v, list):
    #         return '、'.join(v)
    #     return v
    #
    # @validator('pledge_quantity', pre=True)
    # def convert_pledge_coupon_name(cls, v):
    #     if isinstance(v, list):
    #         return '、'.join(v)
    #     return v
    #
    # @validator('pledge_coupon_code', pre=True)
    # def convert_pledge_coupon_code(cls, v):
    #     if isinstance(v, list):ru
    #         return '、'.join(v)
    #     return v
    #
    # @validator('discount_rate', pre=True)
    # def convert_pledge_coupon_name(cls, v):
    #     if isinstance(v, list):
    #         return '、'.join(v)
    #     return v

class AuditOut(AuditBase):
    id: int

class AuditRead(BaseModel):
    id: int

class AuditReply(AuditRead):
    content: str


class ProductionBase(BaseModel):
    tag: str
    production: str

class ProductionCreate(ProductionBase):
    pass

class ProductionRead(ProductionBase):
    id: int

    class Config:
        from_attribute = True

class GroupProductionBase(BaseModel):
    group_id: int
    trader_id: int
    is_deleted: bool

class GroupProductionCreate(GroupProductionBase):
    pass

class GroupProduction(GroupProductionBase):
    id: int

    class Config:
        from_attribute = True

class GroupTraderBase(BaseModel):
    group_id: int
    trader_id: int
    is_deleted: bool

class GroupTraderCreate(GroupTraderBase):
    pass

class GroupTrader(GroupTraderBase):
    id: int

    class Config:
        from_attribute = True

class GroupRobotBase(BaseModel):
    robot_id: int
    group_id: int

class GroupRobotCreate(GroupRobotBase):
    pass

class GroupRobot(GroupRobotBase):
    id: int

    class Config:
        from_attribute = True

class MessageBase(BaseModel):
    sender: str
    receiver: str
    content: str
    position: str
    type:int # 00~11(Binary)，左位表示是文字（0）还是图片（1）？右位表示是在回复别人（1）还是不是（0）？
    reply:Optional[int] = None
    channel:str
    sequence:Optional[int] = None
    result:Optional[str] = None

class MessageCreate(MessageBase):
    pass

class Message(MessageBase):
    id: int

    class Config:
        from_attribute = True

class LogBase(BaseModel):
    operator: str
    target: str
    action: str
    result: bool
    clause: Optional[str] = None
    object: Optional[str]

class LogCreate(LogBase):
    pass

class LogRead(LogBase):
    id: int
    date: datetime

    class Config:
        from_attribute = True

class GroupMemberBase(BaseModel):
    number: str
    name: str
    group_id: int
    permission: int
    role:Literal['regular','trader','robot']

class GroupMemberFilter(BaseModel):
    keyword: Optional[str] = None
    page:int = 1
    id:int

class GroupMemberPermissionFilter(BaseModel):
    id:int
    permission:int

class GroupMemberCreate(GroupMemberBase):
    pass

class GroupMemberRead(GroupMemberBase):
    id: int

    class Config:
        from_attribute = True

class GroupMemberUpdateItem(BaseModel):
    id: int
    group_id: Optional[int] = None
    permission: int
    number: str
    name: str
    role: Literal['regular','trader','robot']
    permissions: Optional[List[str]] = []

class GroupMemberBatchUpdate(BaseModel):
    group_id: int
    member: List[GroupMemberUpdateItem]

class GroupBase(BaseModel):
    type: Literal["QQ", "微信"]
    name: constr(max_length=200)
    number: constr(max_length=200)
    listening: bool
    robot: List[int]

class GroupUpdate(BaseModel):
    name: Optional[constr(max_length=200)] = None
    number: Optional[constr(max_length=200)] = None
    listening: Optional[bool] = None

class GroupFilter(GroupUpdate):
    type: Optional[Literal["QQ", "微信"]] = None
    status: Optional[bool] = None
    page: Optional[int] = 1
    count: Optional[int] = 15

class GroupModifyRobot(BaseModel):
    id: int
    nickname: str
    status: bool
    account: str
    startupStatus: Optional[str] = None
    tel: Optional[str] = None
    gender: Optional[bool] = None
    type: Optional[str] = None
    uni: Optional[int] = None
    email: Optional[str] = None
    enable: Optional[bool] = None

class GroupModifyListenedRobot(BaseModel):
    id: int
    nickname: str
    status: bool
    account: str

class GroupModifyProduct(BaseModel):
    id: int
    name: str
    fund_code:Optional[str] = None
    production_code:Optional[str] = None

class GroupModify(BaseModel):
    type: str
    name: str
    listening: bool
    id: int
    number: str
    robot: List[GroupModifyRobot]
    listened_robot: List[GroupModifyListenedRobot]
    product: List[GroupModifyProduct]
    trader: List = []
    consigner: List = []
    membersList: List = []

    class Config:
        extra = "ignore"

class GroupOut(GroupBase):
    id: int

    class Config:
        from_attributes = True

class ScriptBase(BaseModel):
    content: str
    enable: bool
    id: int

class ScriptOut(ScriptBase):
    environment: str

    class Config:
        from_attribute = True

class RobotBase(BaseModel):
    type: Literal["微信", "QQ"]
    account: constr(max_length=255)
    gender: bool
    tel: constr(max_length=100)
    email: EmailStr
    status: bool = True
    enable: bool = True


class RobotModify(BaseModel):
    id:int
    enable: Optional[bool] = Field(None, description="是否启用")
    tel: Optional[str] = Field(None, description="联系电话")
    email: Optional[str] = Field(None, description="电子邮件")
    nickname: Optional[str] = Field(None, description="昵称")


class RobotCreate(BaseModel):
    nickname:str
    type: Literal["微信", "QQ"]
    account: constr(max_length=255)
    gender: bool
    tel: constr(max_length=100)
    email: EmailStr
    enable: bool = False
    uni: Optional[int] = Field(None, description="唯一标识符，自动生成")

class RobotOut(RobotBase):
    id: int
    uni: str
    nickname: str

    class Config:
        from_attributes = True

class UserBase(BaseModel):
    pass

class UserLogin(BaseModel):
    username: constr(max_length=100)
    password: constr(max_length=100)


class UserModify(UserBase):
    id: int
    role: Optional[Literal['salesman', 'admin', 'root']] = None
    active: Optional[bool] = None
    disable: Optional[bool] = None
    organization: Optional[str] = None
    tel: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[EmailStr] = None
    qq:Optional[str]
    wechat:Optional[str]


class UserMaskedOut(UserBase):
    id: Optional[int] = None
    username: constr(max_length=100)
    nickname: constr(max_length=100)
    role: Literal['salesman', 'admin', 'root'] = 'salesman'
    organization: constr(max_length=200)
    tel: constr(max_length=60)
    phone: constr(max_length=60)
    email: EmailStr
    active: bool
    disable: bool
    qq:str
    wechat:str

class UserCreate(UserMaskedOut):
    password: constr(max_length=100)

    class Config:
        from_attribute = True

#####################################
#           FBI WARNING             #
#       以下 Schemas 已不使用         #
#                                   #
#                                   #
#####################################


# class EwxMessage(BaseModel):
#     message: str
#     atme: bool
#     type: int = Field(...)
#     sender: str = Field(..., max_length=100)
#     receiver: str = Field(..., max_length=100)
#     position: str = Field(...,max_length=255)
#
# class EwxMessageCreate(EwxMessage):
#     pass
#
# class EwxMessageRead(EwxMessage):
#     id: int
#     date: datetime
#
#     class Config:
#         from_attribute = True
#
#
# class EwxGroupBase(BaseModel):
#     robot: int
#     groupAnnouncement: Optional[str] = None
#     groupName: str
#     createTime: date
#     level: int
#
# class EwxGroupCreate(EwxGroupBase):
#     pass
#
# class EwxGroupUpdate(EwxGroupBase):
#     pass
#
# class EwxGroupOut(EwxGroupBase):
#     id: int
#
#     class Config:
#         from_attribute = True
#
# class EwxRobotBase(BaseModel):
#     robotId: str
#     name: str
#     corporation: str
#     sumInfo: str
#     openCallback: bool
#     encryptType: bool
#     callbackUrl: str
#     enableAdd: bool
#     replyAll: bool
#     robotKeyCheck: bool
#     callBackRequestType: int
#     robotType: bool
#     firstLogin: date
#     is_active: bool
#     authExpir: date
#
# class EwxRobotCreate(EwxRobotBase):
#     pass
#
# class EwxRobotUpdate(EwxRobotBase):
#     pass
#
# ###
# class RobotActivityUpdate(BaseModel):
#     robot_id: int
#     is_active: bool
#
# class EwxRobotOut(EwxRobotBase):
#     id: int
#
#     class Config:
#         from_attribute = True
#
# class QQGroupBase(BaseModel):
#     group_id: str
#     group_name: str
#     member: int
#     max_member: int
#     robot: int
#
# class QQGroupCreate(QQGroupBase):
#     pass
#
# class QQGroupUpdate(QQGroupBase):
#     pass
#
# class QQGroupOut(QQGroupBase):
#     id: int
#
#     class Config:
#         from_attribute = True
#
# class QQRobotBase(BaseModel):
#     account: str
#     nickname: str
#     personal_note: Optional[str] = None
#     sex: bool
#     address: str
#     is_active: bool
#     bearer: str
#
# class QQRobotCreate(QQRobotBase):
#     pass
#
# class QQRobotUpdate(QQRobotBase):
#     pass
#
# class QQRobotOut(QQRobotBase):
#     id: int
#
#     class Config:
#         from_attribute = True
